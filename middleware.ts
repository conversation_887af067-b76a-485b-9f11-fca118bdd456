import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function middleware(req: NextRequest) {
  // Skip static files and API routes
  if (req.nextUrl.pathname.startsWith('/_next/') ||
      req.nextUrl.pathname.startsWith('/api/') ||
      req.nextUrl.pathname.includes('.')) {
    return NextResponse.next();
  }

  // Define public routes that don't require authentication
  const publicRoutes = ['/login', '/pending-approval'];
  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route));

  // For public routes, allow access
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Create a response object to pass to the Supabase client
  let response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });

  // Create Supabase client for server-side operations
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  try {
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    // If no session, redirect to login
    if (!session || sessionError) {
      const loginUrl = new URL('/login', req.url);
      return NextResponse.redirect(loginUrl);
    }

    // Get user profile with approval status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approval_status, role')
      .eq('id', session.user.id)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching user profile:', profileError);
      const loginUrl = new URL('/login', req.url);
      return NextResponse.redirect(loginUrl);
    }

    // Handle different approval statuses
    if (profile.approval_status === 'rejected') {
      // Sign out rejected users and redirect to login
      await supabase.auth.signOut();
      const loginUrl = new URL('/login', req.url);
      return NextResponse.redirect(loginUrl);
    }

    if (profile.approval_status === 'pending') {
      // Redirect pending users to pending approval page
      const pendingUrl = new URL('/pending-approval', req.url);
      return NextResponse.redirect(pendingUrl);
    }

    if (profile.approval_status === 'approved') {
      // Allow approved users to access protected routes
      return response;
    }

    // Default: redirect to login if approval status is unknown
    const loginUrl = new URL('/login', req.url);
    return NextResponse.redirect(loginUrl);

  } catch (error) {
    console.error('Middleware error:', error);
    const loginUrl = new URL('/login', req.url);
    return NextResponse.redirect(loginUrl);
  }
}

// Configure which routes this middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};