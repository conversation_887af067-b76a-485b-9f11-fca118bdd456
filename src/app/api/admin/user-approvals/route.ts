import { NextRequest, NextResponse } from 'next/server';
import { supabase as supabaseServerClient } from '@/lib/supabaseClient';

// GET - Fetch users by approval status
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = req.nextUrl;
    const status = searchParams.get('status');

    // Build query based on status filter
    let query = supabaseServerClient
      .from('profiles')
      .select('id, email, name, role, approval_status, created_at')
      .order('created_at', { ascending: false });

    // Apply status filter if provided
    if (status && status !== 'all') {
      query = query.eq('approval_status', status);
    }

    const { data: users, error } = await query;

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: users || []
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error fetching users for approval:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// PUT - Update user approval status
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, approval_status, adminId } = body;

    // Validate required fields
    if (!userId || !approval_status) {
      return NextResponse.json({
        success: false,
        error: 'User ID and approval status are required'
      }, { status: 400 });
    }

    // Validate approval status
    if (!['pending', 'approved', 'rejected'].includes(approval_status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid approval status'
      }, { status: 400 });
    }

    // TODO: Add admin authentication check here
    // For now, we'll assume the request is from an authenticated admin
    // In a production environment, you should verify the admin's session and role

    // Update the user's approval status
    const { data, error } = await supabaseServerClient
      .from('profiles')
      .update({
        approval_status,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('id, email, name, role, approval_status, created_at')
      .single();

    if (error) {
      throw error;
    }

    // Log the approval action (optional)
    console.log(`User ${userId} approval status updated to ${approval_status} by admin ${adminId}`);

    return NextResponse.json({
      success: true,
      data: data,
      message: `User approval status updated to ${approval_status}`
    }, { status: 200 });

  } catch (error: any) {
    console.error('Error updating user approval status:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}